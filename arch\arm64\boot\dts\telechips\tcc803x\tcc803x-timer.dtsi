// SPDX-License-Identifier: (GPL-2.0-or-later OR MIT)
/*
 * Copyright (C) 2023 Telechips Inc.
 */

#include <dt-bindings/clock/telechips,tcc803x-clks.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>

/ {
	tcc_timer: timer@14300000 { /* tc0~tc5 */
		compatible = "telechips,timer";
		reg = <0x0 0x14300000 0x0 0xa0>;
		interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clk_peri PERI_TCT>;
		clock-frequency = <12000000>;

		tcc-timer-reserved-for-lpo = <0>;
	};
};

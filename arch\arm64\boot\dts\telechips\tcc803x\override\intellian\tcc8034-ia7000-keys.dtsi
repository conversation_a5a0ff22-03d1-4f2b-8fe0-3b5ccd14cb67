// SPDX-License-Identifier: (GPL-2.0-or-later OR MIT)
/*
 * Copyright (C) 2025 Intellian Inc.
 */

#include <dt-bindings/input/linux-event-codes.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	gpio_keys_polled {
		compatible = "gpio-keys-polled";
		#address-cells = <1>;
		#size-cells = <0>;
		poll-interval = <30>;
		//autorepeat;
		button@0 {
			label = "Enter";
			linux,code = <KEY_F1>;
			gpios = <&gpc 8 1>;
		};
	};

	rotary: rotary-encoder {
		compatible = "rotary-encoder";
		gpios = <
				&gpc 12 GPIO_ACTIVE_HIGH
				&gpc 13 GPIO_ACTIVE_HIGH
		>;
		linux,axis = <REL_X>;
		rotary-encoder,relative-axis;
	};
};
// SPDX-License-Identifier: (GPL-2.0-or-later OR MIT)
/*
 * Copyright (C) 2023 Telechips Inc.
 */

#ifndef DT_OVERLAY
#include "tcc803x.dts"
#endif

/ {
	model = "Intellian TCC8034 AIS MKD Main Core";
	board-id = <7000>;
	board-rev = <0x0>;
};

#include "override/tcc803x-dma.dtsi"
#include "override/tcc803x-vpu.dtsi"
#include "override/tcc8030_31pe-cpu.dtsi"
#include "override/intellian/tcc8034-ia7000-gpio.dtsi"
#include "override/intellian/tcc8034-ia7000-gmac.dtsi"
#include "override/intellian/tcc8034-ia7000-uart.dtsi"
#include "override/intellian/tcc8034-ia7000-mmc.dtsi"
#include "override/intellian/tcc8034-ia7000-i2c.dtsi"
#include "override/intellian/tcc8034-ia7000-usb.dtsi"
#include "override/intellian/tcc8034-ia7000-display.dtsi"
#include "override/intellian/tcc8034-ia7000-pwm.dtsi"
#include "override/intellian/tcc8034-ia7000-keys.dtsi"